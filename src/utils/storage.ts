/**
 * 存储工具
 * 提供跨平台的存储操作，支持过期时间设置和类型安全
 */

// ===== 类型定义 =====

/**
 * 存储选项接口
 */
export interface StorageOptions {
  /** 过期时间（毫秒） */
  expire?: number
}

/**
 * 存储数据包装接口
 */
export interface StorageData<T = any> {
  /** 实际数据 */
  data: T
  /** 过期时间戳 */
  expire?: number
}

// ===== 核心存储方法 =====

/**
 * 设置存储数据
 * @param key 存储键名
 * @param value 存储值
 * @param options 存储选项
 */
export function set<T = any>(
  key: string,
  value: T,
  options: StorageOptions = {}
): void {
  try {
    const storageData: StorageData<T> = {
      data: value
    }

    // 设置过期时间
    if (options.expire && options.expire > 0) {
      storageData.expire = Date.now() + options.expire
    }

    const jsonString = JSON.stringify(storageData)

    // #ifdef H5
    // H5平台使用localStorage
    localStorage.setItem(key, jsonString)
    // #endif

    // #ifdef MP || APP
    // 小程序和App使用uni.setStorageSync
    uni.setStorageSync(key, jsonString)
    // #endif
  } catch (error) {
    console.error('Storage set error:', error)
  }
}

/**
 * 获取存储数据
 * @param key 存储键名
 * @param defaultValue 默认值
 * @returns 存储的数据或默认值
 */
export function get<T = any>(key: string, defaultValue?: T): T | undefined {
  try {
    let jsonString: string | null = null

    // #ifdef H5
    // H5平台使用localStorage
    jsonString = localStorage.getItem(key)
    // #endif

    // #ifdef MP || APP
    // 小程序和App使用uni.getStorageSync
    jsonString = uni.getStorageSync(key)
    // #endif

    if (!jsonString) {
      return defaultValue
    }

    const storageData: StorageData<T> = JSON.parse(jsonString)

    // 检查是否过期
    if (storageData.expire && Date.now() > storageData.expire) {
      // 数据已过期，删除并返回默认值
      remove(key)
      return defaultValue
    }

    return storageData.data
  } catch (error) {
    console.error('Storage get error:', error)
    return defaultValue
  }
}

/**
 * 删除指定存储
 * @param key 存储键名
 */
export function remove(key: string): void {
  try {
    // #ifdef H5
    // H5平台使用localStorage
    localStorage.removeItem(key)
    // #endif

    // #ifdef MP || APP
    // 小程序和App使用uni.removeStorageSync
    uni.removeStorageSync(key)
    // #endif
  } catch (error) {
    console.error('Storage remove error:', error)
  }
}

/**
 * 清空所有存储
 */
export function clear(): void {
  try {
    // #ifdef H5
    // H5平台使用localStorage
    localStorage.clear()
    // #endif

    // #ifdef MP || APP
    // 小程序和App使用uni.clearStorageSync
    uni.clearStorageSync()
    // #endif
  } catch (error) {
    console.error('Storage clear error:', error)
  }
}

/**
 * 获取所有存储键名
 * @returns 存储键名数组
 */
export function getKeys(): string[] {
  try {
    // #ifdef H5
    // H5平台使用localStorage
    return Object.keys(localStorage)
    // #endif

    // #ifdef MP || APP
    // 小程序和App使用uni.getStorageInfoSync
    const info = uni.getStorageInfoSync()
    return info.keys
    // #endif
  } catch (error) {
    console.error('Storage getKeys error:', error)
    return []
  }
}

/**
 * 获取存储信息
 * @returns 存储信息对象
 */
export function getInfo(): {
  keys: string[]
  currentSize: number
  limitSize: number
} {
  try {
    // #ifdef H5
    // H5平台计算localStorage使用情况
    const keys = Object.keys(localStorage)
    let currentSize = 0
    keys.forEach(key => {
      const value = localStorage.getItem(key)
      if (value) {
        currentSize += value.length
      }
    })
    return {
      keys,
      currentSize,
      limitSize: 5 * 1024 * 1024 // 5MB（大概值）
    }
    // #endif

    // #ifdef MP || APP
    // 小程序和App使用uni.getStorageInfoSync
    const info = uni.getStorageInfoSync()
    return {
      keys: info.keys,
      currentSize: info.currentSize,
      limitSize: info.limitSize
    }
    // #endif
  } catch (error) {
    console.error('Storage getInfo error:', error)
    return {
      keys: [],
      currentSize: 0,
      limitSize: 0
    }
  }
}

// ===== 便捷方法 =====

/**
 * 设置会话存储（浏览器关闭后失效）
 * @param key 存储键名
 * @param value 存储值
 */
export function setSession<T = any>(key: string, value: T): void {
  // 设置24小时过期
  set(key, value, { expire: 24 * 60 * 60 * 1000 })
}

/**
 * 设置临时存储（1小时后失效）
 * @param key 存储键名
 * @param value 存储值
 */
export function setTemp<T = any>(key: string, value: T): void {
  // 设置1小时过期
  set(key, value, { expire: 60 * 60 * 1000 })
}

/**
 * 检查存储是否存在且未过期
 * @param key 存储键名
 * @returns 是否存在
 */
export function has(key: string): boolean {
  const value = get(key)
  return value !== undefined
}

// ===== 默认导出 =====
export default {
  set,
  get,
  remove,
  clear,
  getKeys,
  getInfo,
  setSession,
  setTemp,
  has
}
