/**
 * 请求适配器
 * 提供跨平台的HTTP请求封装
 */

import type { RequestConfig, ResponseData, ApiError } from './types'
import { showLoading, hideLoading, showToast } from '@/utils/platform'
import { storage } from '@/utils'

// ===== 配置常量 =====

/** 基础URL */
const BASE_URL = import.meta.env.VITE_API_BASE_URL || ''

/** 默认超时时间 */
const DEFAULT_TIMEOUT = 10000

/** 成功状态码 */
const SUCCESS_CODE = 200

// ===== 请求拦截器 =====

/**
 * 请求前处理
 */
function beforeRequest(config: RequestConfig): RequestConfig {
  // 显示loading
  if (config.loading !== false) {
    showLoading({
      title: config.loadingText || '请求中...',
      mask: true
    })
  }

  // 添加认证token
  const token = storage.get('token')
  if (token && config.headers) {
    config.headers.Authorization = `Bearer ${token}`
  }

  // 设置默认请求头
  if (!config.headers) {
    config.headers = {}
  }
  
  if (!config.headers['Content-Type']) {
    config.headers['Content-Type'] = 'application/json'
  }

  return config
}

/**
 * 请求后处理
 */
function afterRequest(config: RequestConfig): void {
  // 隐藏loading
  if (config.loading !== false) {
    hideLoading()
  }
}

/**
 * 响应成功处理
 */
function handleSuccess<T>(response: ResponseData<T>, config: RequestConfig): T {
  afterRequest(config)

  if (response.code === SUCCESS_CODE) {
    return response.data
  } else {
    // 业务错误
    const error: ApiError = {
      code: response.code,
      message: response.message || '请求失败'
    }
    throw error
  }
}

/**
 * 响应错误处理
 */
function handleError(error: any, config: RequestConfig): never {
  afterRequest(config)

  let apiError: ApiError = {
    code: -1,
    message: '网络错误'
  }

  if (error.code) {
    // 业务错误
    apiError = {
      code: error.code,
      message: error.message || '请求失败'
    }
  } else if (error.statusCode) {
    // HTTP错误
    apiError = {
      code: error.statusCode,
      message: getHttpErrorMessage(error.statusCode)
    }
  } else {
    // 网络错误或其他错误
    apiError = {
      code: -1,
      message: error.message || '网络连接失败'
    }
  }

  // 显示错误提示
  if (config.showError !== false) {
    showToast({
      title: apiError.message,
      icon: 'error'
    })
  }

  throw apiError
}

/**
 * 获取HTTP错误消息
 */
function getHttpErrorMessage(statusCode: number): string {
  const messages: Record<number, string> = {
    400: '请求参数错误',
    401: '未授权，请重新登录',
    403: '拒绝访问',
    404: '请求的资源不存在',
    405: '请求方法不允许',
    408: '请求超时',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时'
  }
  return messages[statusCode] || `请求失败 (${statusCode})`
}

// ===== 核心请求方法 =====

/**
 * 发送HTTP请求
 */
async function request<T = any>(config: RequestConfig): Promise<T> {
  // 请求前处理
  const processedConfig = beforeRequest(config)
  
  // 构建完整URL
  const url = processedConfig.url.startsWith('http') 
    ? processedConfig.url 
    : `${BASE_URL}${processedConfig.url}`

  try {
    let response: any

    // #ifdef H5
    // H5平台使用fetch API
    const fetchOptions: RequestInit = {
      method: processedConfig.method || 'GET',
      headers: processedConfig.headers,
      signal: AbortSignal.timeout(processedConfig.timeout || DEFAULT_TIMEOUT)
    }

    // 处理请求参数
    if (processedConfig.method === 'GET' && processedConfig.params) {
      const queryString = new URLSearchParams(processedConfig.params).toString()
      const finalUrl = `${url}${url.includes('?') ? '&' : '?'}${queryString}`
      response = await fetch(finalUrl, fetchOptions)
    } else {
      if (processedConfig.data) {
        fetchOptions.body = JSON.stringify(processedConfig.data)
      }
      response = await fetch(url, fetchOptions)
    }

    if (!response.ok) {
      throw { statusCode: response.status }
    }

    const data = await response.json()
    return handleSuccess<T>(data, processedConfig)
    // #endif

    // #ifdef MP || APP
    // 小程序和App使用uni.request
    response = await new Promise((resolve, reject) => {
      const requestOptions: UniApp.RequestOptions = {
        url,
        method: (processedConfig.method || 'GET') as any,
        header: processedConfig.headers,
        timeout: processedConfig.timeout || DEFAULT_TIMEOUT,
        success: resolve,
        fail: reject
      }

      // 处理请求参数
      if (processedConfig.method === 'GET' && processedConfig.params) {
        const queryString = Object.keys(processedConfig.params)
          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(processedConfig.params![key])}`)
          .join('&')
        requestOptions.url = `${url}${url.includes('?') ? '&' : '?'}${queryString}`
      } else if (processedConfig.data) {
        requestOptions.data = processedConfig.data
      }

      uni.request(requestOptions)
    })

    if (response.statusCode !== 200) {
      throw { statusCode: response.statusCode }
    }

    return handleSuccess<T>(response.data, processedConfig)
    // #endif
  } catch (error) {
    return handleError(error, processedConfig)
  }
}

// ===== 便捷方法 =====

/**
 * GET请求
 */
export function get<T = any>(
  url: string,
  params?: Record<string, any>,
  config?: Partial<RequestConfig>
): Promise<T> {
  return request<T>({
    url,
    method: 'GET',
    params,
    ...config
  })
}

/**
 * POST请求
 */
export function post<T = any>(
  url: string,
  data?: any,
  config?: Partial<RequestConfig>
): Promise<T> {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...config
  })
}

/**
 * PUT请求
 */
export function put<T = any>(
  url: string,
  data?: any,
  config?: Partial<RequestConfig>
): Promise<T> {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...config
  })
}

/**
 * DELETE请求
 */
export function del<T = any>(
  url: string,
  params?: Record<string, any>,
  config?: Partial<RequestConfig>
): Promise<T> {
  return request<T>({
    url,
    method: 'DELETE',
    params,
    ...config
  })
}

// ===== 默认导出 =====
export default {
  request,
  get,
  post,
  put,
  delete: del
}
