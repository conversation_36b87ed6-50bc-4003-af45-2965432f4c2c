/**
 * API接口类型定义
 * 定义请求和响应的数据结构
 */

// ===== 基础类型定义 =====

/**
 * HTTP请求方法
 */
export type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

/**
 * 请求配置接口
 */
export interface RequestConfig {
  /** 请求URL */
  url: string
  /** 请求方法 */
  method?: RequestMethod
  /** 请求参数（GET请求的query参数） */
  params?: Record<string, any>
  /** 请求体数据（POST等请求的body数据） */
  data?: any
  /** 请求头 */
  headers?: Record<string, string>
  /** 超时时间（毫秒） */
  timeout?: number
  /** 是否显示loading */
  loading?: boolean
  /** loading文本 */
  loadingText?: string
  /** 是否显示错误提示 */
  showError?: boolean
}

/**
 * 统一响应数据格式
 */
export interface ResponseData<T = any> {
  /** 状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 是否成功 */
  success: boolean
  /** 时间戳 */
  timestamp?: number
}

/**
 * 分页请求参数
 */
export interface PageParams {
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
}

/**
 * 分页响应数据
 */
export interface PageData<T = any> {
  /** 数据列表 */
  list: T[]
  /** 总数 */
  total: number
  /** 当前页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 总页数 */
  totalPages: number
  /** 是否有下一页 */
  hasNext: boolean
  /** 是否有上一页 */
  hasPrev: boolean
}

// ===== 用户相关类型 =====

/**
 * 用户信息
 */
export interface UserInfo {
  /** 用户ID */
  id: string | number
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname?: string
  /** 头像 */
  avatar?: string
  /** 邮箱 */
  email?: string
  /** 手机号 */
  phone?: string
  /** 性别 */
  gender?: 'male' | 'female' | 'unknown'
  /** 生日 */
  birthday?: string
  /** 创建时间 */
  createTime?: string
  /** 更新时间 */
  updateTime?: string
}

/**
 * 登录参数
 */
export interface LoginParams {
  /** 用户名/手机号/邮箱 */
  username: string
  /** 密码 */
  password: string
  /** 验证码 */
  captcha?: string
  /** 记住我 */
  remember?: boolean
}

/**
 * 登录结果
 */
export interface LoginResult {
  /** 访问令牌 */
  token: string
  /** 刷新令牌 */
  refreshToken?: string
  /** 令牌过期时间 */
  expiresIn?: number
  /** 用户信息 */
  userInfo: UserInfo
}

/**
 * 注册参数
 */
export interface RegisterParams {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 确认密码 */
  confirmPassword: string
  /** 手机号 */
  phone?: string
  /** 邮箱 */
  email?: string
  /** 验证码 */
  captcha: string
}

/**
 * 修改密码参数
 */
export interface ChangePasswordParams {
  /** 旧密码 */
  oldPassword: string
  /** 新密码 */
  newPassword: string
  /** 确认新密码 */
  confirmPassword: string
}

// ===== 文件上传类型 =====

/**
 * 文件上传参数
 */
export interface UploadParams {
  /** 文件路径 */
  filePath: string
  /** 上传字段名 */
  name?: string
  /** 额外参数 */
  formData?: Record<string, any>
}

/**
 * 文件上传结果
 */
export interface UploadResult {
  /** 文件URL */
  url: string
  /** 文件名 */
  filename: string
  /** 文件大小 */
  size: number
  /** 文件类型 */
  type: string
}

// ===== 错误类型 =====

/**
 * API错误信息
 */
export interface ApiError {
  /** 错误码 */
  code: number
  /** 错误消息 */
  message: string
  /** 详细信息 */
  details?: any
  /** 错误堆栈 */
  stack?: string
}
