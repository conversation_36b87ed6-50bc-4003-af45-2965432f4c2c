// ===== 通用样式 =====
// UniApp跨端项目通用样式定义

// ===== 重置样式 =====
* {
  box-sizing: border-box;
}

page {
  background-color: $bg-color-secondary;
  color: $text-color-primary;
  font-size: $font-size-md;
  line-height: $line-height-md;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
}

// ===== 布局类 =====
.container {
  padding: 0 $page-padding;
}

.section {
  padding: $section-padding 0;
}

.page-wrapper {
  min-height: 100vh;
  background-color: $bg-color-secondary;
}

// ===== Flex布局类 =====
.flex {
  display: flex;
}

.flex-center {
  @include flex-center;
}

.flex-between {
  @include flex-between;
}

.flex-start {
  @include flex-start;
}

.flex-end {
  @include flex-end;
}

.flex-column {
  @include flex-column;
}

.flex-column-center {
  @include flex-column-center;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

// ===== 间距类 =====
.m-xs { margin: $space-xs; }
.m-sm { margin: $space-sm; }
.m-md { margin: $space-md; }
.m-lg { margin: $space-lg; }
.m-xl { margin: $space-xl; }

.mt-xs { margin-top: $space-xs; }
.mt-sm { margin-top: $space-sm; }
.mt-md { margin-top: $space-md; }
.mt-lg { margin-top: $space-lg; }
.mt-xl { margin-top: $space-xl; }

.mb-xs { margin-bottom: $space-xs; }
.mb-sm { margin-bottom: $space-sm; }
.mb-md { margin-bottom: $space-md; }
.mb-lg { margin-bottom: $space-lg; }
.mb-xl { margin-bottom: $space-xl; }

.ml-xs { margin-left: $space-xs; }
.ml-sm { margin-left: $space-sm; }
.ml-md { margin-left: $space-md; }
.ml-lg { margin-left: $space-lg; }
.ml-xl { margin-left: $space-xl; }

.mr-xs { margin-right: $space-xs; }
.mr-sm { margin-right: $space-sm; }
.mr-md { margin-right: $space-md; }
.mr-lg { margin-right: $space-lg; }
.mr-xl { margin-right: $space-xl; }

.p-xs { padding: $space-xs; }
.p-sm { padding: $space-sm; }
.p-md { padding: $space-md; }
.p-lg { padding: $space-lg; }
.p-xl { padding: $space-xl; }

.pt-xs { padding-top: $space-xs; }
.pt-sm { padding-top: $space-sm; }
.pt-md { padding-top: $space-md; }
.pt-lg { padding-top: $space-lg; }
.pt-xl { padding-top: $space-xl; }

.pb-xs { padding-bottom: $space-xs; }
.pb-sm { padding-bottom: $space-sm; }
.pb-md { padding-bottom: $space-md; }
.pb-lg { padding-bottom: $space-lg; }
.pb-xl { padding-bottom: $space-xl; }

.pl-xs { padding-left: $space-xs; }
.pl-sm { padding-left: $space-sm; }
.pl-md { padding-left: $space-md; }
.pl-lg { padding-left: $space-lg; }
.pl-xl { padding-left: $space-xl; }

.pr-xs { padding-right: $space-xs; }
.pr-sm { padding-right: $space-sm; }
.pr-md { padding-right: $space-md; }
.pr-lg { padding-right: $space-lg; }
.pr-xl { padding-right: $space-xl; }

// ===== 文本类 =====
.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-md { font-size: $font-size-md; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-xxl { font-size: $font-size-xxl; }

.text-light { font-weight: $font-weight-light; }
.text-normal { font-weight: $font-weight-normal; }
.text-medium { font-weight: $font-weight-medium; }
.text-bold { font-weight: $font-weight-bold; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: $text-color-primary; }
.text-regular { color: $text-color-regular; }
.text-secondary { color: $text-color-secondary; }
.text-placeholder { color: $text-color-placeholder; }

.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-danger { color: $danger-color; }
.text-info { color: $info-color; }

.text-ellipsis {
  @include text-ellipsis(1);
}

.text-ellipsis-2 {
  @include text-ellipsis(2);
}

.text-ellipsis-3 {
  @include text-ellipsis(3);
}

// ===== 背景类 =====
.bg-primary { background-color: $bg-color-primary; }
.bg-secondary { background-color: $bg-color-secondary; }
.bg-tertiary { background-color: $bg-color-tertiary; }

.bg-success { background-color: $success-color; }
.bg-warning { background-color: $warning-color; }
.bg-danger { background-color: $danger-color; }
.bg-info { background-color: $info-color; }

// ===== 边框类 =====
.border { border: 1px solid $border-color-base; }
.border-top { border-top: 1px solid $border-color-base; }
.border-bottom { border-bottom: 1px solid $border-color-base; }
.border-left { border-left: 1px solid $border-color-base; }
.border-right { border-right: 1px solid $border-color-base; }

.border-light { border-color: $border-color-light; }
.border-dark { border-color: $border-color-dark; }

.rounded-sm { border-radius: $border-radius-sm; }
.rounded-md { border-radius: $border-radius-md; }
.rounded-lg { border-radius: $border-radius-lg; }
.rounded-xl { border-radius: $border-radius-xl; }
.rounded-full { border-radius: $border-radius-round; }

// ===== 阴影类 =====
.shadow-light { box-shadow: $box-shadow-light; }
.shadow-base { box-shadow: $box-shadow-base; }
.shadow-dark { box-shadow: $box-shadow-dark; }

// ===== 显示隐藏类 =====
.hidden { display: none !important; }
.visible { display: block !important; }

// ===== 安全区域类 =====
.safe-area-top {
  @include safe-area-padding('top');
}

.safe-area-bottom {
  @include safe-area-padding('bottom');
}

.safe-area-horizontal {
  @include safe-area-padding('horizontal');
}

.safe-area-all {
  @include safe-area-padding('all');
}
