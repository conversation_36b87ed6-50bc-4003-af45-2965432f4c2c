// ===== 平台适配混入 =====
// UniApp跨端项目样式混入定义

// ===== 平台特定样式混入 =====

// 根据平台应用不同样式
@mixin platform-style($h5-style: null, $mp-style: null) {
  // #ifdef H5
  @if $h5-style {
    #{$h5-style}
  }
  // #endif
  
  // #ifdef MP
  @if $mp-style {
    #{$mp-style}
  }
  // #endif
}

// H5专用样式
@mixin h5-only {
  // #ifdef H5
  @content;
  // #endif
}

// 小程序专用样式
@mixin mp-only {
  // #ifdef MP
  @content;
  // #endif
}

// ===== 安全区域适配 =====

// 处理刘海屏等安全区域
@mixin safe-area-padding($direction: 'all') {
  @if $direction == 'top' {
    padding-top: calc(#{$space-md} + #{$safe-area-inset-top});
  } @else if $direction == 'bottom' {
    padding-bottom: calc(#{$space-md} + #{$safe-area-inset-bottom});
  } @else if $direction == 'left' {
    padding-left: calc(#{$space-md} + #{$safe-area-inset-left});
  } @else if $direction == 'right' {
    padding-right: calc(#{$space-md} + #{$safe-area-inset-right});
  } @else if $direction == 'horizontal' {
    padding-left: calc(#{$space-md} + #{$safe-area-inset-left});
    padding-right: calc(#{$space-md} + #{$safe-area-inset-right});
  } @else if $direction == 'vertical' {
    padding-top: calc(#{$space-md} + #{$safe-area-inset-top});
    padding-bottom: calc(#{$space-md} + #{$safe-area-inset-bottom});
  } @else {
    padding-top: calc(#{$space-md} + #{$safe-area-inset-top});
    padding-bottom: calc(#{$space-md} + #{$safe-area-inset-bottom});
    padding-left: calc(#{$space-md} + #{$safe-area-inset-left});
    padding-right: calc(#{$space-md} + #{$safe-area-inset-right});
  }
}

// ===== 1px边框解决方案 =====

// 解决高DPI设备1px边框显示问题
@mixin hairline($direction: 'all', $color: $border-color-base) {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    background-color: $color;
    transform-origin: center;
    
    @if $direction == 'top' {
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      transform: scaleY(0.5);
    } @else if $direction == 'bottom' {
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      transform: scaleY(0.5);
    } @else if $direction == 'left' {
      top: 0;
      left: 0;
      bottom: 0;
      width: 1px;
      transform: scaleX(0.5);
    } @else if $direction == 'right' {
      top: 0;
      right: 0;
      bottom: 0;
      width: 1px;
      transform: scaleX(0.5);
    } @else {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 1px solid $color;
      transform: scale(0.5);
    }
  }
}

// ===== 文本省略 =====

// 文本溢出省略处理
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  } @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lines;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// ===== Flex布局快捷方式 =====

// 水平垂直居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 两端对齐
@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 左对齐
@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

// 右对齐
@mixin flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// 垂直排列
@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// 垂直居中排列
@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// ===== 清除浮动 =====
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// ===== 响应式设计 =====

// 屏幕尺寸断点
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// 响应式媒体查询
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'sm' {
    @media (min-width: $breakpoint-sm) {
      @content;
    }
  } @else if $breakpoint == 'md' {
    @media (min-width: $breakpoint-md) {
      @content;
    }
  } @else if $breakpoint == 'lg' {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  } @else if $breakpoint == 'xl' {
    @media (min-width: $breakpoint-xl) {
      @content;
    }
  }
}

// ===== 动画效果 =====

// 淡入动画
@mixin fade-in($duration: $transition-duration-base) {
  animation: fadeIn $duration $transition-timing-function;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 滑入动画
@mixin slide-in-up($duration: $transition-duration-base) {
  animation: slideInUp $duration $transition-timing-function;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
