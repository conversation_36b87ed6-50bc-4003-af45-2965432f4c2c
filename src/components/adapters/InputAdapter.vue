<template>
  <!-- H5平台使用Vant输入框 -->
  <!-- #ifdef H5 -->
  <van-field
    v-model="inputValue"
    :type="type"
    :label="label"
    :placeholder="placeholder"
    :disabled="disabled"
    :readonly="readonly"
    :required="required"
    :clearable="allowClear"
    :maxlength="maxLength"
    :show-word-limit="showWordLimit"
    :error="error"
    :error-message="errorMessage"
    :class="customClass"
    :style="customStyle"
    @input="handleInput"
    @focus="handleFocus"
    @blur="handleBlur"
    @clear="handleClear"
  />
  <!-- #endif -->

  <!-- 小程序平台使用原生input -->
  <!-- #ifdef MP -->
  <view :class="fieldClass" :style="customStyle">
    <!-- 标签 -->
    <view v-if="label" class="input-adapter__label">
      <text v-if="required" class="input-adapter__required">*</text>
      {{ label }}
    </view>
    
    <!-- 输入框容器 -->
    <view class="input-adapter__control">
      <input
        v-model="inputValue"
        :class="inputClass"
        :type="inputType"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxLength"
        :password="type === 'password'"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      
      <!-- 清除按钮 -->
      <view
        v-if="allowClear && inputValue && !disabled && !readonly"
        class="input-adapter__clear"
        @tap="handleClear"
      >
        ×
      </view>
    </view>
    
    <!-- 字数统计 -->
    <view v-if="showWordLimit && maxLength" class="input-adapter__count">
      {{ inputValue.length }}/{{ maxLength }}
    </view>
    
    <!-- 错误信息 -->
    <view v-if="error && errorMessage" class="input-adapter__error">
      {{ errorMessage }}
    </view>
  </view>
  <!-- #endif -->
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'

// ===== 类型定义 =====
export interface InputProps {
  /** 输入框值 */
  modelValue?: string | number
  /** 输入框类型 */
  type?: 'text' | 'number' | 'password' | 'tel' | 'email'
  /** 标签文本 */
  label?: string
  /** 占位符 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否必填 */
  required?: boolean
  /** 是否显示清除按钮 */
  allowClear?: boolean
  /** 最大长度 */
  maxLength?: number
  /** 是否显示字数统计 */
  showWordLimit?: boolean
  /** 是否错误状态 */
  error?: boolean
  /** 错误信息 */
  errorMessage?: string
  /** 自定义类名 */
  customClass?: string
  /** 自定义样式 */
  customStyle?: string | object
}

// ===== Props定义 =====
const props = withDefaults(defineProps<InputProps>(), {
  modelValue: '',
  type: 'text',
  placeholder: '请输入',
  disabled: false,
  readonly: false,
  required: false,
  allowClear: true,
  maxLength: 200,
  showWordLimit: false,
  error: false
})

// ===== 事件定义 =====
const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  input: [value: string | number]
  focus: [event: Event]
  blur: [event: Event]
  clear: []
}>()

// ===== 响应式数据 =====
const inputValue = ref(props.modelValue)

// ===== 监听props变化 =====
watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue
  }
)

// ===== 计算属性 =====
const inputType = computed(() => {
  // 小程序input组件的type映射
  const typeMap: Record<string, string> = {
    text: 'text',
    number: 'number',
    password: 'text', // 小程序使用password属性
    tel: 'number',
    email: 'text'
  }
  return typeMap[props.type] || 'text'
})

const fieldClass = computed(() => {
  const classes = ['input-adapter']
  
  if (props.disabled) classes.push('input-adapter--disabled')
  if (props.readonly) classes.push('input-adapter--readonly')
  if (props.error) classes.push('input-adapter--error')
  if (props.customClass) classes.push(props.customClass)
  
  return classes.join(' ')
})

const inputClass = computed(() => {
  const classes = ['input-adapter__input']
  
  if (props.disabled) classes.push('input-adapter__input--disabled')
  if (props.readonly) classes.push('input-adapter__input--readonly')
  
  return classes.join(' ')
})

// ===== 事件处理 =====
const handleInput = (event: any) => {
  const value = event.detail?.value || event.target?.value || ''
  inputValue.value = value
  emit('update:modelValue', value)
  emit('input', value)
}

const handleFocus = (event: Event) => {
  emit('focus', event)
}

const handleBlur = (event: Event) => {
  emit('blur', event)
}

const handleClear = () => {
  inputValue.value = ''
  emit('update:modelValue', '')
  emit('input', '')
  emit('clear')
}
</script>

<style lang="scss" scoped>
// 小程序输入框样式（H5使用Vant样式）
/* #ifdef MP */
.input-adapter {
  padding: $space-md 0;
  
  &__label {
    display: flex;
    align-items: center;
    margin-bottom: $space-sm;
    font-size: $font-size-md;
    color: $text-color-primary;
    font-weight: $font-weight-medium;
  }
  
  &__required {
    color: $danger-color;
    margin-right: 4rpx;
  }
  
  &__control {
    position: relative;
    display: flex;
    align-items: center;
    background-color: $bg-color-primary;
    border: 1px solid $border-color-base;
    border-radius: $border-radius-md;
    overflow: hidden;
    
    &:focus-within {
      border-color: $primary-color;
    }
  }
  
  &__input {
    flex: 1;
    height: $input-height;
    padding: 0 $input-padding;
    font-size: $font-size-md;
    color: $text-color-primary;
    background-color: transparent;
    border: none;
    outline: none;
    
    &::placeholder {
      color: $text-color-placeholder;
    }
    
    &--disabled {
      color: $text-color-secondary;
      background-color: $bg-color-secondary;
      cursor: not-allowed;
    }
    
    &--readonly {
      background-color: $bg-color-secondary;
    }
  }
  
  &__clear {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40rpx;
    height: 40rpx;
    margin-right: $space-sm;
    font-size: 32rpx;
    color: $text-color-secondary;
    background-color: $bg-color-tertiary;
    border-radius: 50%;
    cursor: pointer;
    
    &:active {
      background-color: $border-color-base;
    }
  }
  
  &__count {
    margin-top: $space-xs;
    font-size: $font-size-sm;
    color: $text-color-secondary;
    text-align: right;
  }
  
  &__error {
    margin-top: $space-xs;
    font-size: $font-size-sm;
    color: $danger-color;
  }
  
  // 错误状态
  &--error {
    .input-adapter__control {
      border-color: $danger-color;
    }
  }
  
  // 禁用状态
  &--disabled {
    .input-adapter__control {
      background-color: $bg-color-secondary;
      border-color: $border-color-light;
    }
    
    .input-adapter__label {
      color: $text-color-secondary;
    }
  }
  
  // 只读状态
  &--readonly {
    .input-adapter__control {
      background-color: $bg-color-secondary;
    }
  }
}
/* #endif */
</style>
