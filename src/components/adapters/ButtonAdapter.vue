<template>
  <!-- H5平台使用Vant按钮 -->
  <!-- #ifdef H5 -->
  <van-button
    :type="type"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    :round="round"
    :plain="plain"
    :block="block"
    :color="color"
    :class="customClass"
    :style="customStyle"
    @click="handleClick"
  >
    <slot />
  </van-button>
  <!-- #endif -->

  <!-- 小程序平台使用原生button -->
  <!-- #ifdef MP -->
  <button
    :class="buttonClass"
    :style="buttonStyle"
    :disabled="disabled || loading"
    :loading="loading"
    :open-type="openType"
    :form-type="formType"
    @tap="handleClick"
    @getuserinfo="$emit('getuserinfo', $event)"
    @contact="$emit('contact', $event)"
    @getphonenumber="$emit('getphonenumber', $event)"
    @error="$emit('error', $event)"
    @opensetting="$emit('opensetting', $event)"
    @launchapp="$emit('launchapp', $event)"
  >
    <slot />
  </button>
  <!-- #endif -->
</template>

<script setup lang="ts">
import { computed } from 'vue'

// ===== 类型定义 =====
export interface ButtonProps {
  /** 按钮类型 */
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'default'
  /** 按钮尺寸 */
  size?: 'large' | 'normal' | 'small' | 'mini'
  /** 是否加载中 */
  loading?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否圆角 */
  round?: boolean
  /** 是否朴素按钮 */
  plain?: boolean
  /** 是否块级按钮 */
  block?: boolean
  /** 自定义颜色 */
  color?: string
  /** 自定义类名 */
  customClass?: string
  /** 自定义样式 */
  customStyle?: string | object
  /** 小程序开放能力 */
  openType?: string
  /** 表单类型 */
  formType?: 'submit' | 'reset'
}

// ===== Props定义 =====
const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'default',
  size: 'normal',
  loading: false,
  disabled: false,
  round: false,
  plain: false,
  block: false
})

// ===== 事件定义 =====
const emit = defineEmits<{
  click: [event: Event]
  getuserinfo: [event: any]
  contact: [event: any]
  getphonenumber: [event: any]
  error: [event: any]
  opensetting: [event: any]
  launchapp: [event: any]
}>()

// ===== 小程序按钮样式计算 =====
const buttonClass = computed(() => {
  const classes = ['button-adapter']
  
  // 类型样式
  classes.push(`button-adapter--${props.type}`)
  
  // 尺寸样式
  classes.push(`button-adapter--${props.size}`)
  
  // 状态样式
  if (props.loading) classes.push('button-adapter--loading')
  if (props.disabled) classes.push('button-adapter--disabled')
  if (props.round) classes.push('button-adapter--round')
  if (props.plain) classes.push('button-adapter--plain')
  if (props.block) classes.push('button-adapter--block')
  
  // 自定义类名
  if (props.customClass) classes.push(props.customClass)
  
  return classes.join(' ')
})

const buttonStyle = computed(() => {
  let style = ''
  
  // 自定义颜色
  if (props.color) {
    if (props.plain) {
      style += `color: ${props.color}; border-color: ${props.color};`
    } else {
      style += `background-color: ${props.color}; border-color: ${props.color};`
    }
  }
  
  // 自定义样式
  if (props.customStyle) {
    if (typeof props.customStyle === 'string') {
      style += props.customStyle
    } else {
      style += Object.entries(props.customStyle)
        .map(([key, value]) => `${key}: ${value}`)
        .join('; ')
    }
  }
  
  return style
})

// ===== 事件处理 =====
const handleClick = (event: Event) => {
  if (props.disabled || props.loading) {
    return
  }
  emit('click', event)
}
</script>

<style lang="scss" scoped>
// 小程序按钮样式（H5使用Vant样式）
/* #ifdef MP */
.button-adapter {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  line-height: 1;
  text-align: center;
  cursor: pointer;
  transition: all $transition-duration-base $transition-timing-function;
  user-select: none;
  
  // 移除默认样式
  background: none;
  outline: none;
  
  // 尺寸样式
  &--large {
    height: $button-height-large;
    padding: 0 $space-lg;
    font-size: $button-font-size-large;
  }
  
  &--normal {
    height: $button-height-normal;
    padding: 0 $space-md;
    font-size: $button-font-size-normal;
  }
  
  &--small {
    height: $button-height-small;
    padding: 0 $space-sm;
    font-size: $button-font-size-small;
  }
  
  &--mini {
    height: $button-height-mini;
    padding: 0 $space-xs;
    font-size: $button-font-size-mini;
  }
  
  // 类型样式
  &--default {
    background-color: $bg-color-primary;
    border-color: $border-color-base;
    color: $text-color-primary;
    
    &:not(.button-adapter--disabled):active {
      background-color: $bg-color-secondary;
    }
  }
  
  &--primary {
    background-color: $primary-color;
    border-color: $primary-color;
    color: #ffffff;
    
    &:not(.button-adapter--disabled):active {
      background-color: $primary-dark;
    }
  }
  
  &--success {
    background-color: $success-color;
    border-color: $success-color;
    color: #ffffff;
    
    &:not(.button-adapter--disabled):active {
      opacity: 0.8;
    }
  }
  
  &--warning {
    background-color: $warning-color;
    border-color: $warning-color;
    color: #ffffff;
    
    &:not(.button-adapter--disabled):active {
      opacity: 0.8;
    }
  }
  
  &--danger {
    background-color: $danger-color;
    border-color: $danger-color;
    color: #ffffff;
    
    &:not(.button-adapter--disabled):active {
      opacity: 0.8;
    }
  }
  
  // 朴素按钮
  &--plain {
    background-color: transparent;
    
    &.button-adapter--default {
      color: $text-color-primary;
    }
    
    &.button-adapter--primary {
      color: $primary-color;
    }
    
    &.button-adapter--success {
      color: $success-color;
    }
    
    &.button-adapter--warning {
      color: $warning-color;
    }
    
    &.button-adapter--danger {
      color: $danger-color;
    }
  }
  
  // 圆角按钮
  &--round {
    border-radius: $button-height-normal / 2;
    
    &.button-adapter--large {
      border-radius: $button-height-large / 2;
    }
    
    &.button-adapter--small {
      border-radius: $button-height-small / 2;
    }
    
    &.button-adapter--mini {
      border-radius: $button-height-mini / 2;
    }
  }
  
  // 块级按钮
  &--block {
    display: flex;
    width: 100%;
  }
  
  // 禁用状态
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  // 加载状态
  &--loading {
    cursor: default;
  }
}
/* #endif */
</style>
