<template>
  <view class="login-page">
    <view class="login-container">
      <!-- 标题 -->
      <view class="login-header">
        <text class="login-title">欢迎登录</text>
        <text class="login-subtitle">请输入您的账号信息</text>
      </view>

      <!-- 登录表单 -->
      <view class="login-form">
        <!-- 用户名输入框 -->
        <InputAdapter
          v-model="formData.username"
          label="用户名"
          placeholder="请输入用户名/手机号/邮箱"
          :maxLength="50"
          :required="true"
          :error="errors.username"
          :errorMessage="errors.username"
        />

        <!-- 密码输入框 -->
        <InputAdapter
          v-model="formData.password"
          type="password"
          label="密码"
          placeholder="请输入密码"
          :maxLength="20"
          :required="true"
          :error="errors.password"
          :errorMessage="errors.password"
        />

        <!-- 记住我 -->
        <view class="login-options">
          <label class="checkbox-wrapper">
            <checkbox
              :checked="formData.remember"
              @change="handleRememberChange"
            />
            <text class="checkbox-text">记住我</text>
          </label>
        </view>

        <!-- 登录按钮 -->
        <ButtonAdapter
          type="primary"
          size="large"
          :loading="loading"
          :block="true"
          @click="handleLogin"
        >
          登录
        </ButtonAdapter>

        <!-- 其他操作 -->
        <view class="login-actions">
          <text class="action-link" @tap="handleRegister">注册账号</text>
          <text class="action-link" @tap="handleForgotPassword">忘记密码</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { InputAdapter, ButtonAdapter } from '@/components/adapters'
import { useUserStore } from '@/store'
import { showToast, navigateBack, switchTab } from '@/utils/platform'
import type { LoginParams } from '@/api/types'

// ===== 响应式数据 =====
const userStore = useUserStore()
const loading = ref(false)

// 表单数据
const formData = reactive<LoginParams>({
  username: '',
  password: '',
  remember: false
})

// 错误信息
const errors = reactive({
  username: '',
  password: ''
})

// ===== 表单验证 =====
const validateForm = (): boolean => {
  // 清空之前的错误
  errors.username = ''
  errors.password = ''

  let isValid = true

  // 验证用户名
  if (!formData.username.trim()) {
    errors.username = '请输入用户名'
    isValid = false
  }

  // 验证密码
  if (!formData.password.trim()) {
    errors.password = '请输入密码'
    isValid = false
  } else if (formData.password.length < 6) {
    errors.password = '密码长度不能少于6位'
    isValid = false
  }

  return isValid
}

// ===== 事件处理 =====
const handleRememberChange = (event: any) => {
  formData.remember = event.detail.value.length > 0
}

const handleLogin = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    await userStore.login(formData)
    
    showToast({
      title: '登录成功',
      icon: 'success'
    })

    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      // 跳转到首页
      switchTab('/pages/index/index')
    }, 1500)
  } catch (error: any) {
    console.error('登录失败:', error)
    showToast({
      title: error.message || '登录失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

const handleRegister = () => {
  showToast({
    title: '注册功能开发中',
    icon: 'none'
  })
}

const handleForgotPassword = () => {
  showToast({
    title: '忘记密码功能开发中',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  @include flex-center;
  padding: $space-lg;
}

.login-container {
  width: 100%;
  max-width: 400px;
  background-color: $bg-color-primary;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-dark;
  overflow: hidden;
}

.login-header {
  padding: $space-xl $space-lg $space-lg;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.login-title {
  display: block;
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  margin-bottom: $space-sm;
}

.login-subtitle {
  display: block;
  font-size: $font-size-sm;
  opacity: 0.9;
}

.login-form {
  padding: $space-lg;
}

.login-options {
  margin: $space-lg 0;
}

.checkbox-wrapper {
  @include flex-start;
  cursor: pointer;
}

.checkbox-text {
  margin-left: $space-sm;
  font-size: $font-size-sm;
  color: $text-color-regular;
}

.login-actions {
  @include flex-between;
  margin-top: $space-lg;
}

.action-link {
  font-size: $font-size-sm;
  color: $primary-color;
  cursor: pointer;
  
  &:active {
    opacity: 0.7;
  }
}
</style>
