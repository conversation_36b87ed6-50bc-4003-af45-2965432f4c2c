<template>
  <view class="login-page">
    <view class="login-container">
      <!-- 标题 -->
      <view class="login-header">
        <text class="login-title">欢迎登录</text>
        <text class="login-subtitle">请输入您的账号信息</text>
      </view>

      <!-- 登录表单 -->
      <view class="login-form">
        <!-- 用户名输入框 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            用户名
          </view>
          <input
            v-model="formData.username"
            class="form-input"
            :class="{ 'form-input--error': errors.username }"
            placeholder="请输入用户名/手机号/邮箱"
            maxlength="50"
          />
          <view v-if="errors.username" class="form-error">
            {{ errors.username }}
          </view>
        </view>

        <!-- 密码输入框 -->
        <view class="form-item">
          <view class="form-label">
            <text class="required">*</text>
            密码
          </view>
          <input
            v-model="formData.password"
            type="password"
            class="form-input"
            :class="{ 'form-input--error': errors.password }"
            placeholder="请输入密码"
            maxlength="20"
          />
          <view v-if="errors.password" class="form-error">
            {{ errors.password }}
          </view>
        </view>

        <!-- 记住我 -->
        <view class="login-options">
          <label class="checkbox-wrapper">
            <checkbox
              :checked="formData.remember"
              @change="handleRememberChange"
            />
            <text class="checkbox-text">记住我</text>
          </label>
        </view>

        <!-- 登录按钮 -->
        <button
          class="login-button"
          :class="{ 'login-button--loading': loading }"
          :disabled="loading"
          @tap="handleLogin"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>

        <!-- 其他操作 -->
        <view class="login-actions">
          <text class="action-link" @tap="handleRegister">注册账号</text>
          <text class="action-link" @tap="handleForgotPassword">忘记密码</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useUserStore } from '@/store'
import { showToast, navigateBack, switchTab } from '@/utils/platform'
import type { LoginParams } from '@/api/types'

// ===== 响应式数据 =====
const userStore = useUserStore()
const loading = ref(false)

// 表单数据
const formData = reactive<LoginParams>({
  username: '',
  password: '',
  remember: false
})

// 错误信息
const errors = reactive({
  username: '',
  password: ''
})

// ===== 表单验证 =====
const validateForm = (): boolean => {
  // 清空之前的错误
  errors.username = ''
  errors.password = ''

  let isValid = true

  // 验证用户名
  if (!formData.username.trim()) {
    errors.username = '请输入用户名'
    isValid = false
  }

  // 验证密码
  if (!formData.password.trim()) {
    errors.password = '请输入密码'
    isValid = false
  } else if (formData.password.length < 6) {
    errors.password = '密码长度不能少于6位'
    isValid = false
  }

  return isValid
}

// ===== 事件处理 =====
const handleRememberChange = (event: any) => {
  formData.remember = event.detail.value.length > 0
}

const handleLogin = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    await userStore.login(formData)
    
    showToast({
      title: '登录成功',
      icon: 'success'
    })

    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      // 跳转到首页
      switchTab('/pages/index/index')
    }, 1500)
  } catch (error: any) {
    console.error('登录失败:', error)
    showToast({
      title: error.message || '登录失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

const handleRegister = () => {
  showToast({
    title: '注册功能开发中',
    icon: 'none'
  })
}

const handleForgotPassword = () => {
  showToast({
    title: '忘记密码功能开发中',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  @include flex-center;
  padding: $space-lg;
}

.login-container {
  width: 100%;
  max-width: 400px;
  background-color: $bg-color-primary;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-dark;
  overflow: hidden;
}

.login-header {
  padding: $space-xl $space-lg $space-lg;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.login-title {
  display: block;
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  margin-bottom: $space-sm;
}

.login-subtitle {
  display: block;
  font-size: $font-size-sm;
  opacity: 0.9;
}

.login-form {
  padding: $space-lg;
}

.form-item {
  margin-bottom: $space-lg;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: $space-sm;
  font-size: $font-size-md;
  color: $text-color-primary;
  font-weight: $font-weight-medium;
}

.required {
  color: $danger-color;
  margin-right: 4rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 $space-md;
  font-size: $font-size-md;
  color: $text-color-primary;
  background-color: $bg-color-primary;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-md;
  box-sizing: border-box;

  &::placeholder {
    color: $text-color-placeholder;
  }

  &:focus {
    border-color: $primary-color;
    outline: none;
  }

  &--error {
    border-color: $danger-color;
  }
}

.form-error {
  margin-top: $space-xs;
  font-size: $font-size-sm;
  color: $danger-color;
}

.login-button {
  width: 100%;
  height: 88rpx;
  background-color: $primary-color;
  border: none;
  border-radius: $border-radius-md;
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  color: #ffffff;
  cursor: pointer;
  transition: all $transition-duration-base;

  &:active {
    background-color: $primary-dark;
  }

  &--loading {
    opacity: 0.7;
    cursor: not-allowed;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.login-options {
  margin: $space-lg 0;
}

.checkbox-wrapper {
  @include flex-start;
  cursor: pointer;
}

.checkbox-text {
  margin-left: $space-sm;
  font-size: $font-size-sm;
  color: $text-color-regular;
}

.login-actions {
  @include flex-between;
  margin-top: $space-lg;
}

.action-link {
  font-size: $font-size-sm;
  color: $primary-color;
  cursor: pointer;
  
  &:active {
    opacity: 0.7;
  }
}
</style>
