<template>
  <view class="profile-page">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-avatar">
        <image :src="userStore.avatar" class="avatar-image" />
      </view>
      <view class="user-info">
        <text class="user-name">{{ userStore.displayName || '未登录' }}</text>
        <text v-if="userStore.userInfo?.email" class="user-email">
          {{ userStore.userInfo.email }}
        </text>
      </view>
      <view class="user-actions">
        <ButtonAdapter
          v-if="!userStore.isLoggedIn"
          type="primary"
          size="small"
          @click="handleLogin"
        >
          登录
        </ButtonAdapter>
        <ButtonAdapter
          v-else
          type="default"
          size="small"
          @click="handleEditProfile"
        >
          编辑
        </ButtonAdapter>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-title">功能菜单</view>
      <view class="menu-list">
        <view
          v-for="item in menuItems"
          :key="item.id"
          class="menu-item"
          @tap="handleMenuClick(item)"
        >
          <view class="menu-icon">
            <text class="icon">{{ item.icon }}</text>
          </view>
          <view class="menu-content">
            <text class="menu-name">{{ item.name }}</text>
            <text v-if="item.desc" class="menu-desc">{{ item.desc }}</text>
          </view>
          <view class="menu-arrow">
            <text class="arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view v-if="userStore.isLoggedIn" class="logout-section">
      <ButtonAdapter
        type="danger"
        size="large"
        :block="true"
        :loading="logoutLoading"
        @click="handleLogout"
      >
        退出登录
      </ButtonAdapter>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ButtonAdapter } from '@/components/adapters'
import { useUserStore } from '@/store'
import { showToast, showModal, navigateTo } from '@/utils/platform'

// ===== 响应式数据 =====
const userStore = useUserStore()
const logoutLoading = ref(false)

// ===== 菜单配置 =====
const menuItems = computed(() => [
  {
    id: 'userInfo',
    name: '个人信息',
    desc: '查看和编辑个人资料',
    icon: '👤',
    needLogin: true
  },
  {
    id: 'settings',
    name: '设置',
    desc: '应用设置和偏好',
    icon: '⚙️',
    needLogin: false
  },
  {
    id: 'help',
    name: '帮助与反馈',
    desc: '使用帮助和问题反馈',
    icon: '❓',
    needLogin: false
  },
  {
    id: 'about',
    name: '关于我们',
    desc: '应用版本和相关信息',
    icon: 'ℹ️',
    needLogin: false
  }
])

// ===== 事件处理 =====
const handleLogin = () => {
  navigateTo('/pages/user/login')
}

const handleEditProfile = () => {
  showToast({
    title: '编辑功能开发中',
    icon: 'none'
  })
}

const handleMenuClick = (item: any) => {
  // 检查是否需要登录
  if (item.needLogin && !userStore.isLoggedIn) {
    showModal({
      title: '提示',
      content: '请先登录后再使用此功能',
      showCancel: true,
      confirmText: '去登录',
      cancelText: '取消'
    }).then((result) => {
      if (result.confirm) {
        handleLogin()
      }
    })
    return
  }

  // 处理菜单点击
  switch (item.id) {
    case 'userInfo':
      showToast({
        title: '个人信息功能开发中',
        icon: 'none'
      })
      break
    case 'settings':
      showToast({
        title: '设置功能开发中',
        icon: 'none'
      })
      break
    case 'help':
      showToast({
        title: '帮助功能开发中',
        icon: 'none'
      })
      break
    case 'about':
      showModal({
        title: '关于我们',
        content: 'UniApp跨端项目\n版本：v1.0.0\n基于Vue3 + TypeScript + Vant构建',
        showCancel: false,
        confirmText: '确定'
      })
      break
    default:
      break
  }
}

const handleLogout = async () => {
  const result = await showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    showCancel: true,
    confirmText: '退出',
    cancelText: '取消'
  })

  if (!result.confirm) {
    return
  }

  logoutLoading.value = true

  try {
    await userStore.logout()
    
    showToast({
      title: '已退出登录',
      icon: 'success'
    })
  } catch (error: any) {
    console.error('退出登录失败:', error)
    showToast({
      title: error.message || '退出失败',
      icon: 'error'
    })
  } finally {
    logoutLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background-color: $bg-color-secondary;
  padding: $space-lg;
}

.user-card {
  @include flex-start;
  background-color: $bg-color-primary;
  border-radius: $border-radius-lg;
  padding: $space-lg;
  margin-bottom: $space-lg;
  box-shadow: $box-shadow-light;
}

.user-avatar {
  margin-right: $space-md;
}

.avatar-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: $bg-color-tertiary;
}

.user-info {
  flex: 1;
  @include flex-column;
  justify-content: center;
}

.user-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  margin-bottom: $space-xs;
}

.user-email {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.user-actions {
  margin-left: $space-md;
}

.menu-section {
  margin-bottom: $space-lg;
}

.menu-title {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  margin-bottom: $space-md;
  padding: 0 $space-sm;
}

.menu-list {
  background-color: $bg-color-primary;
  border-radius: $border-radius-lg;
  overflow: hidden;
}

.menu-item {
  @include flex-start;
  padding: $space-lg;
  border-bottom: 1px solid $border-color-light;
  cursor: pointer;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: $bg-color-secondary;
  }
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  @include flex-center;
  background-color: $bg-color-secondary;
  border-radius: $border-radius-md;
  margin-right: $space-md;
}

.icon {
  font-size: 40rpx;
}

.menu-content {
  flex: 1;
  @include flex-column;
  justify-content: center;
}

.menu-name {
  font-size: $font-size-md;
  color: $text-color-primary;
  margin-bottom: $space-xs;
}

.menu-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.menu-arrow {
  @include flex-center;
  width: 40rpx;
  height: 40rpx;
}

.arrow {
  font-size: $font-size-lg;
  color: $text-color-placeholder;
}

.logout-section {
  margin-top: $space-xl;
}
</style>
