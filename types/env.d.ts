/**
 * 环境变量类型声明
 */

/// <reference types="vite/client" />

interface ImportMetaEnv {
  /** 环境标识 */
  readonly VITE_ENV: 'development' | 'production'
  
  /** 应用标题 */
  readonly VITE_APP_TITLE: string
  
  /** 应用版本 */
  readonly VITE_APP_VERSION: string
  
  /** API基础URL */
  readonly VITE_API_BASE_URL: string
  
  /** API超时时间 */
  readonly VITE_API_TIMEOUT: string
  
  /** 是否开启调试 */
  readonly VITE_DEBUG: string
  
  /** 是否开启控制台日志 */
  readonly VITE_CONSOLE_LOG: string
  
  /** 是否开启Mock */
  readonly VITE_ENABLE_MOCK: string
  
  /** 是否开启VConsole */
  readonly VITE_ENABLE_VCONSOLE: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
