# UniApp + Vue3 + TypeScript + Vant 跨端项目架构设计文档

## 📋 项目概述

本文档描述了一个基于 UniApp + Vue3 + TypeScript + Vant 的跨端项目架构设计，支持小程序和H5双端运行。

## 🎯 技术栈

**核心技术栈**
- 开发框架：UniApp (CLI模式)
- 前端框架：Vue3 (Composition API)
- 类型系统：TypeScript 4.9+
- UI组件库：Vant 4.x (H5) + uni-ui (小程序)
- 状态管理：Pinia 2.x
- 构建工具：Vite 4.x
- 代码规范：ESLint + Prettier
- 包管理器：pnpm

## 🏗️ 项目结构

**目录结构说明**
- `src/components/` - 组件库（预留目录，按需添加组件）
- `src/pages/` - 页面目录
  - `index/` - 首页
  - `user/` - 用户相关页面
  - `common/` - 公共页面
- `src/api/` - 接口管理
  - `modules/` - 接口模块
  - `types/` - 接口类型定义
  - `request.ts` - 请求封装
- `src/store/` - 状态管理
  - `modules/` - store模块
  - `index.ts` - store入口
- `src/utils/` - 工具函数
  - `platform.ts` - 平台判断
  - `storage.ts` - 存储封装
  - `common.ts` - 通用工具
- `src/types/` - 全局类型定义
- `src/styles/` - 样式文件
  - `variables.scss` - 变量定义
  - `mixins.scss` - 混入
  - `common.scss` - 通用样式
- `src/static/` - 静态资源
- `src/platforms/` - 平台特定配置
  - `h5/` - H5平台配置
  - `mp-weixin/` - 微信小程序配置
- `dist/` - 构建输出目录
- `types/` - 类型声明文件
- 配置文件：
  - `.env.development` - 开发环境配置
  - `.env.production` - 生产环境配置
  - `pages.json` - 页面配置
  - `manifest.json` - 应用配置
  - `uni.scss` - 全局样式变量
  - `vite.config.ts` - Vite配置
  - `tsconfig.json` - TypeScript配置
  - `package.json` - 项目配置

## 🔧 核心配置文件

### 1. package.json 配置要点
**脚本命令：**
- `dev:h5` - H5开发模式
- `dev:mp` - 小程序开发模式
- `build:h5` - H5生产构建
- `build:mp` - 小程序生产构建
- `type-check` - TypeScript类型检查
- `lint` - 代码规范检查
- `lint:fix` - 自动修复代码规范

**核心依赖：**
- UniApp相关：`@dcloudio/uni-app`、`@dcloudio/uni-components`等
- Vue3生态：`vue`、`pinia`
- UI组件：`vant`
- 开发工具：`typescript`、`vite`、`eslint`、`prettier`

### 2. vite.config.ts 配置要点
**路径别名配置：**
- `@` - src目录
- `@utils` - 工具函数目录
- `@api` - API接口目录
- `@store` - 状态管理目录
- `@types` - 类型定义目录

**样式预处理：**
- 自动导入全局SCSS变量和混入
- 支持SCSS语法

**环境变量：**
- `__PLATFORM__` - 当前编译平台标识

**开发服务器：**
- 端口：3000
- 主机：0.0.0.0（支持局域网访问）

### 3. tsconfig.json 配置要点
**编译选项：**
- 目标版本：ES2020
- 模块系统：ESNext
- 严格模式：启用
- JSX保留：preserve

**路径映射：**
- 与vite.config.ts保持一致的别名配置

**类型声明：**
- UniApp类型：`@dcloudio/types`
- Vite客户端类型：`vite/client`

**包含文件：**
- src目录下所有文件
- types目录下类型声明文件

**排除文件：**
- node_modules
- dist构建输出目录

## 🎨 样式系统设计

### 1. 设计令牌系统 (styles/variables.scss)

**颜色系统：**
- 主色调：`--primary-color: #1989fa`（品牌蓝）
- 功能色：成功绿、警告橙、危险红、信息灰
- 中性色：文本主色、常规色、次要色、占位符色
- 背景色：主背景白、次背景灰、三级背景
- 边框色：浅色、基础色、深色

**间距系统（rpx单位，自动适配）：**
- `$space-xs: 8rpx` - 超小间距
- `$space-sm: 16rpx` - 小间距
- `$space-md: 24rpx` - 中等间距
- `$space-lg: 32rpx` - 大间距
- `$space-xl: 48rpx` - 超大间距
- `$space-xxl: 64rpx` - 特大间距

**字体系统：**
- `$font-size-xs: 20rpx` - 超小字体
- `$font-size-sm: 24rpx` - 小字体
- `$font-size-md: 28rpx` - 中等字体（默认）
- `$font-size-lg: 32rpx` - 大字体
- `$font-size-xl: 36rpx` - 超大字体
- `$font-size-xxl: 40rpx` - 特大字体

**圆角系统：**
- `$border-radius-sm: 4rpx` - 小圆角
- `$border-radius-md: 8rpx` - 中等圆角
- `$border-radius-lg: 12rpx` - 大圆角
- `$border-radius-xl: 16rpx` - 超大圆角

**阴影系统：**
- `$box-shadow-light` - 轻阴影
- `$box-shadow-base` - 基础阴影
- `$box-shadow-dark` - 深阴影

### 2. 平台适配混入 (styles/mixins.scss)

**核心混入功能：**

**平台特定样式混入：**
- `@mixin platform-style()` - 根据平台应用不同样式
- 支持H5和小程序平台的条件编译

**安全区域适配：**
- `@mixin safe-area-padding()` - 处理刘海屏等安全区域
- 支持上下左右四个方向的安全区域适配
- 兼容iOS和Android设备

**1px边框解决方案：**
- `@mixin hairline()` - 解决高DPI设备1px边框显示问题
- 支持上下左右及全边框
- 使用transform缩放实现真正的1px效果

**文本省略：**
- `@mixin text-ellipsis()` - 文本溢出省略处理
- 支持单行和多行文本省略
- 兼容性良好的实现方案

**Flex布局快捷方式：**
- `@mixin flex-center` - 水平垂直居中
- `@mixin flex-between` - 两端对齐
- `@mixin flex-column` - 垂直排列



## 🔌 API请求封装

### 1. 请求适配器设计

**核心接口定义：**
- `RequestConfig` - 请求配置接口
- `ResponseData<T>` - 统一响应数据格式

**适配器特性：**
- 支持GET、POST、PUT、DELETE方法
- 统一的错误处理机制
- 可配置的超时时间
- 灵活的请求头设置

**平台适配策略：**
- **H5平台：** 使用原生fetch API
- **小程序平台：** 使用uni.request API
- 通过条件编译实现平台差异化处理

**请求处理流程：**
1. 构建完整的请求URL（baseURL + 接口路径）
2. 处理查询参数和请求体数据
3. 设置请求头（Content-Type等）
4. 发送请求并处理响应
5. 统一的错误处理和异常抛出

**便捷方法：**
- `get()` - GET请求
- `post()` - POST请求
- `put()` - PUT请求
- `delete()` - DELETE请求

### 2. API模块化管理

**模块设计原则：**
- 按业务功能划分API模块
- 统一的接口类型定义
- 清晰的方法命名规范

**用户API模块示例：**
- **接口类型：** UserInfo、LoginParams、LoginResult
- **核心方法：**
  - `login()` - 用户登录
  - `getUserInfo()` - 获取用户信息
  - `updateUserInfo()` - 更新用户信息
  - `logout()` - 退出登录

**模块组织结构：**
- `api/modules/` - 各业务模块API
- `api/types/` - 接口类型定义
- `api/request.ts` - 请求适配器

## 🗄️ 状态管理设计

### 1. Pinia状态管理架构

**核心特性：**
- 使用Pinia 2.x作为状态管理库
- 支持TypeScript类型推导
- 集成持久化插件
- 模块化状态管理

**持久化存储适配：**
- **H5平台：** 使用localStorage
- **小程序平台：** 使用uni.getStorageSync/setStorageSync
- 统一的存储接口，自动平台适配

**Store入口配置：**
- 创建Pinia实例
- 配置持久化插件
- 设置跨平台存储适配器

### 2. 用户状态管理模块

**状态设计：**
- `token` - 用户认证令牌
- `userInfo` - 用户基本信息
- `isLoggedIn` - 登录状态计算属性

**核心方法：**
- `login()` - 用户登录处理
  - 调用登录API
  - 保存token和用户信息
  - 支持平台特定登录逻辑
- `logout()` - 退出登录处理
  - 调用退出API
  - 清除本地状态
  - 错误处理机制
- `fetchUserInfo()` - 获取用户信息
  - 更新用户信息状态
  - 异常处理

**持久化配置：**
- 存储key：`user-store`
- 持久化字段：`token`、`userInfo`
- 自动恢复登录状态

**模块化设计：**
- 使用Composition API风格
- 清晰的状态和方法分离
- 完善的TypeScript类型支持

## 🛠️ 工具函数库

### 1. 平台判断工具设计

**核心功能：**
- 运行时平台检测
- 系统信息获取
- 跨平台UI交互
- 页面导航管理

**平台判断方法：**
- `isH5()` - 检测是否为H5平台
- `isMp()` - 检测是否为小程序平台
- 使用条件编译实现准确判断

**系统信息：**
- `getSystemInfo()` - 获取设备系统信息
- 返回Promise包装的系统信息对象
- 包含设备型号、系统版本、屏幕信息等

**UI交互适配：**
- `showToast()` - 显示提示消息
  - H5使用Vant的showToast
  - 小程序使用uni.showToast
- `showLoading()` - 显示加载状态
  - H5使用Vant的showLoadingToast
  - 小程序使用uni.showLoading
- `hideLoading()` - 隐藏加载状态
  - 统一的加载隐藏处理

**页面导航：**
- `navigateTo()` - 保留当前页面，跳转到新页面
- `redirectTo()` - 关闭当前页面，跳转到新页面
- `navigateBack()` - 返回上一页面
- 统一使用uni API，保证跨平台兼容

### 2. 存储工具设计

**核心特性：**
- 跨平台存储适配
- 支持过期时间设置
- 类型安全的存储操作
- 自动序列化/反序列化

**接口设计：**
- `StorageOptions` - 存储选项接口
  - `expire` - 过期时间（毫秒）
- `StorageData<T>` - 存储数据包装接口
  - `data` - 实际数据
  - `expire` - 过期时间戳

**核心方法：**
- `set<T>()` - 设置存储数据
  - 支持泛型类型
  - 可选的过期时间设置
  - 自动JSON序列化
- `get<T>()` - 获取存储数据
  - 类型安全的数据获取
  - 自动过期检查和清理
  - JSON反序列化处理
- `remove()` - 删除指定存储
- `clear()` - 清空所有存储

**平台适配：**
- **H5平台：** 使用localStorage API
- **小程序平台：** 使用uni.setStorageSync/getStorageSync API
- 统一的接口，透明的平台差异处理

**过期机制：**
- 存储时记录过期时间戳
- 获取时自动检查是否过期
- 过期数据自动清理

## 📱 页面配置

### 1. pages.json 配置说明

**页面路由配置：**
- 首页：`pages/index/index`
  - 启用下拉刷新
  - H5隐藏原生导航栏
  - 小程序自定义导航栏样式
- 登录页：`pages/user/login`
  - 自定义导航栏样式
- 个人中心：`pages/user/profile`
  - 标准导航栏配置

**TabBar配置：**
- 颜色方案：
  - 默认颜色：`#999999`
  - 选中颜色：`#1989fa`（品牌蓝）
  - 背景颜色：`#ffffff`
- Tab项目：
  - 首页：home图标
  - 我的：user图标
- 图标资源：静态图标 + 选中状态图标

**全局样式配置：**
- 导航栏：黑色文字，白色背景
- 页面背景：`#f5f5f5`浅灰色
- 应用标题：`UniApp项目`

**H5平台配置：**
- 路由模式：history模式
- 基础路径：`/`
- 优化选项：启用TreeShaking
- 页面标题：`UniApp H5应用`

**小程序平台配置：**
- AppID：需要配置实际的小程序AppID
- 开发设置：
  - 关闭URL检查
  - 启用ES6语法
  - 启用增强编译
  - 启用PostCSS
  - 启用代码压缩
- 组件支持：启用自定义组件
- 权限配置：位置权限说明

### 2. manifest.json 配置说明

**应用基本信息：**
- 应用名称：`my-uniapp-project`
- 应用ID：`__UNI__XXXXXXX`（UniApp自动生成）
- 应用描述：`UniApp跨端项目`
- 版本信息：v1.0.0 (100)

**App-Plus配置（原生App）：**
- 启用自定义组件
- 使用uni-app样式编译器
- 编译器版本：3
- 启动屏配置：
  - 渲染前显示启动屏
  - 等待首页渲染完成
  - 自动关闭启动屏
- Android权限配置：
  - 网络状态权限
  - 文件系统权限
  - 震动权限
  - 相机权限
  - 设备信息权限等

**小程序配置：**
- AppID：需要配置实际的小程序AppID
- 编译设置：
  - 关闭URL合法性检查
  - 启用ES6转ES5
  - 启用增强编译
  - 启用PostCSS
  - 启用代码压缩
  - 启用新特性支持
- 组件支持：启用自定义组件
- 权限配置：位置权限
- 隐私信息：位置信息获取

**H5配置：**
- 页面标题：`UniApp H5应用`
- HTML模板：`index.html`
- 路由配置：history模式
- 优化选项：启用TreeShaking
- 统计功能：关闭uni统计

## 🚀 开发工作流

### 1. 环境配置管理

**开发环境配置 (.env.development)：**
- 环境标识：`development`
- API地址：开发服务器地址
- 应用标题：开发环境标识

**生产环境配置 (.env.production)：**
- 环境标识：`production`
- API地址：生产服务器地址
- 应用标题：生产环境标识

**环境变量使用：**
- 在代码中通过`import.meta.env.VITE_*`访问
- 支持不同环境的配置切换
- 构建时自动注入对应环境变量

### 2. 代码规范配置

**ESLint配置要点：**
- 运行环境：浏览器、ES2021、Node.js
- 扩展配置：
  - ESLint推荐规则
  - Vue3 TypeScript配置
  - Vue3基础规则
- 解析器：TypeScript解析器
- 插件：Vue、TypeScript
- 自定义规则：
  - 关闭组件命名检查
  - TypeScript类型警告
  - 未使用变量警告
- 全局变量：uni、wx、getCurrentPages、getApp

**Prettier配置要点：**
- 不使用分号
- 使用单引号
- 缩进：2个空格
- 不使用尾随逗号
- 行宽：100字符
- 换行符：LF

## 📋 最佳实践

### 1. 组件开发规范
- **API风格：** 使用Composition API，提供更好的TypeScript支持
- **命名规范：** 组件名使用PascalCase（如：UserCard、ProductList）
- **类型定义：** Props使用TypeScript接口定义，确保类型安全
- **事件处理：** 使用defineEmits定义事件，提供类型推导
- **样式作用域：** 使用scoped避免样式污染

### 2. 代码组织规范
- **模块化：** 按功能模块组织代码，便于维护和扩展
- **类型安全：** 使用TypeScript严格模式，减少运行时错误
- **接口管理：** API接口统一管理，便于维护和复用
- **工具分类：** 工具函数按用途分类，提高代码复用性

### 3. 性能优化建议
- **包体积优化：** 使用条件编译，只打包目标平台代码
- **资源优化：** 图片资源压缩，使用合适的格式和尺寸
- **按需加载：** 第三方库按需引入，减少不必要的代码
- **缓存策略：** 合理使用缓存，提升用户体验

### 4. 调试与测试
- **开发调试：** 使用console.log和浏览器开发者工具
- **小程序调试：** 使用微信开发者工具的调试功能
- **真机测试：** 在真实设备上验证功能和性能
- **跨平台测试：** 确保在不同平台上的一致性

---

**文档版本：** v1.0
**创建时间：** 2025-01-18
**适用范围：** UniApp + Vue3 + TypeScript + Vant 跨端项目
