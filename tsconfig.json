{
  "compilerOptions": {
    // 基础配置
    "target": "ES2020",
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "moduleResolution": "node",
    "allowJs": true,
    "checkJs": false,
    
    // 严格模式配置
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    
    // JSX配置
    "jsx": "preserve",
    "jsxImportSource": "vue",
    
    // 模块解析配置
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    
    // 输出配置
    "declaration": false,
    "declarationMap": false,
    "sourceMap": true,
    "removeComments": false,
    
    // 路径映射配置（与vite.config.ts保持一致）
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/components/*"],
      "@utils/*": ["src/utils/*"],
      "@api/*": ["src/api/*"],
      "@store/*": ["src/store/*"],
      "@types/*": ["src/types/*"],
      "@styles/*": ["src/styles/*"],
      "@static/*": ["src/static/*"]
    },
    
    // 类型声明
    "types": [
      "@dcloudio/types",
      "vite/client",
      "node"
    ],
    
    // 实验性功能
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
  },
  
  // 包含的文件
  "include": [
    "src/**/*",
    "types/**/*",
    "*.ts",
    "*.vue"
  ],
  
  // 排除的文件
  "exclude": [
    "node_modules",
    "dist",
    "unpackage",
    "**/*.js"
  ],
  
  // Vue文件支持
  "vueCompilerOptions": {
    "target": 3
  }
}
